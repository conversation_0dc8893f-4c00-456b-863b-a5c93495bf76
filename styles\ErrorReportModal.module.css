.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 10000;
  padding: 20px;
  overflow-y: auto;
  backdrop-filter: blur(5px);
}

.modal {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  max-width: 500px;
  width: 100%;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  margin: 60px auto 60px auto;
  backdrop-filter: blur(20px);
  /* Scroll customizado */
  scrollbar-width: thin;
  scrollbar-color: rgba(29, 185, 84, 0.5) rgba(255, 255, 255, 0.1);
}

/* Scrollbar customizada para Webkit */
.modal::-webkit-scrollbar {
  width: 8px;
}

.modal::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.modal::-webkit-scrollbar-thumb {
  background: rgba(29, 185, 84, 0.5);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.modal::-webkit-scrollbar-thumb:hover {
  background: rgba(29, 185, 84, 0.7);
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.headerContent {
  display: flex;
  align-items: center;
  gap: 12px;
}

.headerIcon {
  color: #ff9800;
  font-size: 1.5rem;
}

.header h3 {
  margin: 0;
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 600;
}

.closeButton {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  opacity: 0.7;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.1);
  opacity: 1;
}

.closeButton:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.content {
  padding: 24px;
  max-height: calc(90vh - 100px);
  overflow-y: auto;
}

/* Conteúdo de sucesso */
.successContent {
  text-align: center;
  color: #ffffff;
}

.successIcon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.successContent h4 {
  margin: 0 0 12px 0;
  color: #4caf50;
  font-size: 1.3rem;
}

.successContent p {
  margin: 8px 0;
  opacity: 0.9;
  line-height: 1.5;
}

.emailInfo {
  font-size: 0.9rem;
  opacity: 0.7;
  margin-top: 16px !important;
}

/* Formulário */
.form {
  color: #ffffff;
}

.typeSelector {
  margin-bottom: 20px;
}

.typeSelector label {
  display: block;
  margin-bottom: 12px;
  font-weight: 500;
  color: #ffffff;
}

.typeOptions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.typeOption {
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  flex: 1;
  min-width: 120px;
}

.typeOption:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.typeOption.active {
  background: rgba(29, 185, 84, 0.2);
  border-color: rgba(29, 185, 84, 0.5);
  color: #1db954;
  font-weight: 600;
}

.gameContext {
  margin-bottom: 20px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gameContext label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #ffffff;
  font-size: 0.9rem;
}

.contextInfo {
  color: #4ecdc4;
  font-size: 0.95rem;
}

.formGroup {
  margin-bottom: 20px;
}

.formGroup label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #ffffff;
}

.textarea,
.input {
  width: 100%;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  resize: vertical;
  font-family: inherit;
}

.textarea {
  min-height: 100px;
}

.textarea:focus,
.input:focus {
  outline: none;
  border-color: rgba(29, 185, 84, 0.5);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.2);
}

.textarea::placeholder,
.input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.errorMessage {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  border-radius: 8px;
  padding: 12px;
  color: #ff5252;
  font-size: 0.9rem;
  margin-bottom: 16px;
}

.buttonGroup {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
}

.primaryButton,
.secondaryButton {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
}

.primaryButton {
  background: linear-gradient(135deg, #1db954, #1ed760);
  color: #ffffff;
}

.primaryButton:hover {
  background: linear-gradient(135deg, #1ed760, #1db954);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(29, 185, 84, 0.3);
}

.primaryButton:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.secondaryButton {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.secondaryButton:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.secondaryButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsividade */
@media (max-width: 768px) {
  .overlay {
    padding: 10px;
  }
  
  .modal {
    max-height: calc(100vh - 80px);
    margin: 40px auto 40px auto;
  }
  
  .header {
    padding: 16px 20px;
  }
  
  .content {
    padding: 20px;
  }
  
  .typeOptions {
    flex-direction: column;
  }
  
  .typeOption {
    min-width: auto;
  }
  
  .buttonGroup {
    flex-direction: column-reverse;
  }
  
  .primaryButton,
  .secondaryButton {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .modal {
    max-height: calc(100vh - 40px);
    margin: 20px auto 20px auto;
  }

  .header h3 {
    font-size: 1.1rem;
  }

  .content {
    padding: 16px;
  }

  .textarea,
  .input {
    padding: 10px;
    font-size: 0.9rem;
  }
}
