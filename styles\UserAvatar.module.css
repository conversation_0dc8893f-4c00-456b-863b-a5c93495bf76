/* Container do avatar */
.avatarContainer {
  position: relative;
  display: inline-block;
}

.avatarContainer.editable {
  cursor: pointer;
}

/* Avatar principal */
.avatar {
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1db954 0%, #1ed760 100%);
  border: 2px solid #1db954;
  transition: all 0.2s ease;
}

.avatarContainer.editable:hover .avatar {
  transform: scale(1.05);
  border-color: #1ed760;
  box-shadow: 0 4px 15px rgba(29, 185, 84, 0.3);
}

/* Imagem do avatar */
.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

/* Emoji do avatar */
.avatarEmoji {
  font-size: 60%;
  line-height: 1;
}

/* Ícone padrão */
.defaultIcon {
  color: white;
  font-size: 50%;
}

/* Tamanhos */
.small .avatar {
  width: 32px;
  height: 32px;
}

.small .avatarEmoji {
  font-size: 16px;
}

.small .defaultIcon {
  font-size: 14px;
}

.medium .avatar {
  width: 50px;
  height: 50px;
}

.medium .avatarEmoji {
  font-size: 24px;
}

.medium .defaultIcon {
  font-size: 20px;
}

.large .avatar {
  width: 80px;
  height: 80px;
}

.large .avatarEmoji {
  font-size: 40px;
}

.large .defaultIcon {
  font-size: 32px;
}

.xlarge .avatar {
  width: 120px;
  height: 120px;
}

.xlarge .avatarEmoji {
  font-size: 60px;
}

.xlarge .defaultIcon {
  font-size: 48px;
}

/* Ícone de edição */
.editIcon {
  position: absolute;
  bottom: 0;
  right: 0;
  background: #1db954;
  border: 2px solid white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
  transition: all 0.2s ease;
}

.avatarContainer.editable:hover .editIcon {
  background: #1ed760;
  transform: scale(1.1);
}

/* Tamanhos do ícone de edição */
.small .editIcon {
  width: 16px;
  height: 16px;
  font-size: 8px;
  bottom: -2px;
  right: -2px;
}

.medium .editIcon {
  width: 20px;
  height: 20px;
  font-size: 9px;
  bottom: -1px;
  right: -1px;
}

.large .editIcon {
  width: 28px;
  height: 28px;
  font-size: 12px;
  bottom: 2px;
  right: 2px;
}

.xlarge .editIcon {
  width: 36px;
  height: 36px;
  font-size: 16px;
  bottom: 15px;
  right: 4px;
}

/* Estados especiais */
.avatarContainer.online .avatar {
  border-color: #22c55e;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.3);
}

.avatarContainer.offline .avatar {
  border-color: #6b7280;
  opacity: 0.7;
}

.avatarContainer.loading .avatar {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .large .avatar {
    width: 70px;
    height: 70px;
  }

  .large .avatarEmoji {
    font-size: 35px;
  }

  .large .defaultIcon {
    font-size: 28px;
  }

  .xlarge .avatar {
    width: 100px;
    height: 100px;
  }

  .xlarge .avatarEmoji {
    font-size: 50px;
  }

  .xlarge .defaultIcon {
    font-size: 40px;
  }
}

@media (max-width: 480px) {
  .large .avatar {
    width: 60px;
    height: 60px;
  }

  .large .avatarEmoji {
    font-size: 30px;
  }

  .large .defaultIcon {
    font-size: 24px;
  }

  .xlarge .avatar {
    width: 80px;
    height: 80px;
  }

  .xlarge .avatarEmoji {
    font-size: 40px;
  }

  .xlarge .defaultIcon {
    font-size: 32px;
  }
}
