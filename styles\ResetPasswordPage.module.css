/* Estilos para a Página de Reset de Senha */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #0f0f0f 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.card {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 20px;
  padding: 40px;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

/* Header */
.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: #ffffff;
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 10px 0;
  background: linear-gradient(45deg, #4ade80, #22c55e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.emailInfo {
  color: #cccccc;
  font-size: 14px;
  margin: 0;
}

.emailInfo strong {
  color: #4ade80;
}

/* Formulário */
.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.label svg {
  color: #4ade80;
}

.passwordContainer {
  position: relative;
  display: flex;
  align-items: center;
}

.input {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  color: #ffffff;
  font-size: 16px;
  transition: all 0.2s;
  width: 100%;
  box-sizing: border-box;
}

.passwordContainer .input {
  padding-right: 50px;
}

.input:focus {
  outline: none;
  border-color: #4ade80;
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(74, 222, 128, 0.1);
}

.input::placeholder {
  color: #888;
}

.input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.passwordToggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.passwordToggle:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}

.passwordToggle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Mensagem de erro */
.error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 12px;
  color: #fca5a5;
  font-size: 14px;
  text-align: center;
}

/* Botão de submit */
.submitButton {
  background: linear-gradient(45deg, #4ade80, #22c55e);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 18px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  margin-top: 10px;
}

.submitButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 222, 128, 0.3);
}

.submitButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* Footer */
.footer {
  margin-top: 30px;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.backLink {
  color: #4ade80;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.2s;
  display: inline-block;
}

.backLink:hover {
  background: rgba(74, 222, 128, 0.1);
  color: #22c55e;
}

/* Estados de Loading, Erro e Sucesso */
.loading {
  text-align: center;
  padding: 40px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(74, 222, 128, 0.3);
  border-top: 3px solid #4ade80;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p {
  color: #cccccc;
  font-size: 16px;
  margin: 0;
}

.errorState,
.successState {
  text-align: center;
  padding: 40px 20px;
}

.errorIcon,
.successIcon {
  font-size: 64px;
  margin-bottom: 20px;
}

.errorIcon {
  color: #ef4444;
}

.successIcon {
  color: #4ade80;
}

.errorState h2,
.successState h2 {
  color: #ffffff;
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 15px 0;
}

.errorState p,
.successState p {
  color: #cccccc;
  font-size: 16px;
  margin: 0 0 10px 0;
  line-height: 1.5;
}

.actions {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.homeButton,
.loginButton {
  background: linear-gradient(45deg, #4ade80, #22c55e);
  color: white;
  text-decoration: none;
  border-radius: 12px;
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.2s;
  display: inline-block;
}

.homeButton:hover,
.loginButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 222, 128, 0.3);
}

/* Responsividade */
@media (max-width: 480px) {
  .container {
    padding: 10px;
  }
  
  .card {
    padding: 30px 20px;
    margin: 10px;
    max-width: none;
  }
  
  .header h1 {
    font-size: 24px;
  }
  
  .input {
    font-size: 16px; /* Evita zoom no iOS */
  }
}

/* Animações */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  animation: fadeIn 0.5s ease-out;
}
