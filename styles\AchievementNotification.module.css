/* Container das notificações */
.notificationContainer {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 15px;
  pointer-events: none;
}

/* Notificação base */
.notification {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  border: 2px solid;
  min-width: 350px;
  max-width: 400px;
  position: relative;
  pointer-events: auto;
  animation: slideInRight 0.5s ease-out, fadeOut 0.5s ease-in 4.5s forwards;
  overflow: hidden;
}

.notification::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #1db954, #1ed760);
  animation: progressBar 5s linear forwards;
}

/* Animações */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

@keyframes progressBar {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Botão de fechar */
.closeButton {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.2s;
  font-size: 0.9rem;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Notificação de conquista */
.achievement {
  border-color: #1db954;
}

.achievementContent {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  color: white;
}

.achievementIcon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #1db954, #1ed760);
  border-radius: 50%;
  flex-shrink: 0;
  animation: bounce 0.6s ease-out;
}

.achievementIcon svg {
  font-size: 1.5rem;
  color: white;
  position: absolute;
}

.emoji {
  font-size: 2rem;
  position: absolute;
  animation: pulse 2s infinite;
}

.achievementInfo {
  flex: 1;
  padding-right: 20px;
}

.achievementTitle {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1db954;
  margin-bottom: 5px;
}

.achievementName {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  margin-bottom: 5px;
}

.achievementDesc {
  font-size: 0.9rem;
  color: #b3b3b3;
  margin-bottom: 8px;
  line-height: 1.4;
}

.xpReward {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1ed760;
  background: rgba(29, 185, 84, 0.2);
  padding: 4px 8px;
  border-radius: 8px;
  display: inline-block;
}

/* Notificação de level up */
.levelup {
  border-color: #F59E0B;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d1810 100%);
}

.levelup::before {
  background: linear-gradient(90deg, #F59E0B, #FCD34D);
}

.levelUpContent {
  display: flex;
  align-items: center;
  gap: 15px;
  color: white;
}

.levelUpIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #F59E0B, #FCD34D);
  border-radius: 50%;
  flex-shrink: 0;
  animation: starSpin 1s ease-out;
}

.levelUpIcon svg {
  font-size: 2rem;
  color: white;
}

.levelUpInfo {
  flex: 1;
  padding-right: 20px;
}

.levelUpTitle {
  font-size: 1.1rem;
  font-weight: 700;
  color: #F59E0B;
  margin-bottom: 5px;
}

.levelUpLevel {
  font-size: 1.4rem;
  font-weight: 700;
  color: white;
  margin-bottom: 5px;
}

.levelUpDesc {
  font-size: 0.9rem;
  color: #b3b3b3;
  line-height: 1.4;
}

/* Animações especiais */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -10px, 0);
  }
  70% {
    transform: translate3d(0, -5px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes starSpin {
  0% {
    transform: rotate(0deg) scale(0.5);
  }
  50% {
    transform: rotate(180deg) scale(1.2);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .notificationContainer {
    top: 10px;
    right: 10px;
    left: 10px;
  }

  .notification {
    min-width: auto;
    max-width: none;
  }

  .achievementContent,
  .levelUpContent {
    gap: 10px;
  }

  .achievementIcon,
  .levelUpIcon {
    width: 50px;
    height: 50px;
  }

  .achievementIcon svg,
  .levelUpIcon svg {
    font-size: 1.2rem;
  }

  .emoji {
    font-size: 1.5rem;
  }
}
