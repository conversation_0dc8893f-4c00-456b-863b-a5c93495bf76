.globalStatsContainer {
  margin: 1rem 0;
  text-align: center;
}

.globalStatsMessage {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  letter-spacing: 0.5px;
  display: inline-block;
  max-width: 100%;
}

/* Responsividade */
@media (max-width: 768px) {
  .globalStatsMessage {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
  }
}

/* Animação de entrada */
.globalStatsContainer {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
