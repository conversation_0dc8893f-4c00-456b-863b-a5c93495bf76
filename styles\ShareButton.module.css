.shareContainer {
  position: relative;
  display: inline-block;
}

.shareButton {
  background: linear-gradient(135deg, #1db954, #1ed760);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(29, 185, 84, 0.3);
}

.shareButton:hover {
  background: linear-gradient(135deg, #1ed760, #1db954);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(29, 185, 84, 0.4);
}

.shareButton:active {
  transform: translateY(0);
}

.shareMenu {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 8px;
  background: rgba(18, 18, 18, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  z-index: 1000;
  min-width: 200px;
}

.shareOptions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.shareOption {
  background: transparent;
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
  text-align: left;
}

.shareOption:hover {
  background: rgba(255, 255, 255, 0.1);
}

.shareOption:active {
  background: rgba(255, 255, 255, 0.2);
}

/* Cores específicas para cada plataforma */
.shareOption:nth-child(1):hover {
  background: rgba(29, 161, 242, 0.2); /* Twitter blue */
}

.shareOption:nth-child(2):hover {
  background: rgba(24, 119, 242, 0.2); /* Facebook blue */
}

.shareOption:nth-child(3):hover {
  background: rgba(108, 117, 125, 0.2); /* Copy gray */
}

/* Ícones */
.shareOption svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* Responsividade */
@media (max-width: 768px) {
  .shareMenu {
    position: fixed;
    top: auto;
    bottom: 20px;
    left: 20px;
    right: 20px;
    transform: none;
    margin-top: 0;
    min-width: auto;
  }
  
  .shareOptions {
    flex-direction: row;
    justify-content: space-around;
  }
  
  .shareOption {
    flex: 1;
    justify-content: center;
    padding: 16px 8px;
    flex-direction: column;
    gap: 4px;
    font-size: 12px;
  }
  
  .shareOption svg {
    width: 20px;
    height: 20px;
  }
}

/* Animações */
.shareMenu {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@media (max-width: 768px) {
  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

/* Estado de sucesso para o botão de copiar */
.shareOption:nth-child(3) svg {
  transition: all 0.2s ease;
}

.shareOption:nth-child(3):hover svg {
  color: #28a745; /* Verde para indicar sucesso */
}
